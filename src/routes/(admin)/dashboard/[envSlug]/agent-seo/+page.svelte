<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import {
    Search,
    Download,
    Target,
    TrendingUp,
    FileText,
    Clock,
    User,
    Bot,
    ChevronRight,
    Sparkles,
    Filter,
    Copy,
    FileDown,
    BarChart,
    BookOpen,
    Lightbulb,
  } from "lucide-svelte"
  import { onMount } from "svelte"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  let selectedMessageId = ""
  let outputFormat = "summary"
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let showFilters = false
  let targetAudience = ""
  let regionFocus = ""
  let funnelStage = "awareness"
  
  // Animated placeholder examples
  const placeholderExamples = [
    "Find long-tail keywords for organic skincare...",
    "Analyze competitor keywords for project management tools...",
    "Discover niche keywords for sustainable fashion brands...",
    "Research local SEO keywords for coffee shops in Seattle..."
  ]
  
  // Interactive card templates
  const interactiveCards = [
    {
      icon: Search,
      title: "Niche Keyword Discovery",
      description: "Find untapped long-tail keywords in your specific niche",
      prompt: "Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"
    },
    {
      icon: TrendingUp,
      title: "Competitor Gap Analysis",
      description: "Identify keyword opportunities your competitors are missing",
      prompt: "Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space"
    },
    {
      icon: BarChart,
      title: "Content Cluster Mapping",
      description: "Build topical authority with strategic content clusters",
      prompt: "Create a content cluster strategy around [Main Topic] for a [Business Type] targeting [Goal]"
    }
  ]

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Build enhanced message with filters and format
    let enhancedMessage = input.trim()
    const filters = []
    if (targetAudience) filters.push(`Target audience: ${targetAudience}`)
    if (regionFocus) filters.push(`Region focus: ${regionFocus}`)
    if (funnelStage) filters.push(`Funnel stage: ${funnelStage}`)
    if (filters.length > 0) {
      enhancedMessage = `${enhancedMessage}\n\nAdditional context: ${filters.join(", ")}`
    }
    enhancedMessage += `\n\nOutput format: ${outputFormat}`
    
    const userMessage = enhancedMessage
    const messageId = generateId()
    input = ""
    isLoading = true

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/agent-seo`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Add assistant response to chat
      const assistantMessageId = generateId()
      messages.update((msgs) => [
        ...msgs,
        {
          id: assistantMessageId,
          role: "assistant",
          content: data.response,
          timestamp: new Date(),
          isReport: true,
        },
      ])

      selectedMessageId = assistantMessageId
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but an error occurred while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function downloadAsMarkdown(message: Message) {
    const businessName = extractBusinessName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${businessName || "seo-strategy"}-${timestamp}.md`

    const markdownContent = `# SEO Strategy Report
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}

---

${message.content}

---

*Report generated by Robynn.ai SEO Strategist Agent*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractBusinessName(content: string): string {
    // Simple extraction - looks for business name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Business:") || line.includes("Company:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("SEO") &&
        !line.includes("Strategy")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Convert markdown-like formatting to HTML for better display
    return content
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>',
      )
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>',
      )
      .replace(
        /^\*\*(.*?)\*\*/gim,
        '<strong class="font-bold text-foreground">$1</strong>',
      )
      .replace(
        /^\* (.*$)/gim,
        '<li class="ml-4 text-muted-foreground">• $1</li>',
      )
      .replace(
        /\n\n/gim,
        '</p><p class="text-muted-foreground leading-relaxed mb-4">',
      )
      .replace(
        /^(?!<[h|l|s])(.+)$/gim,
        '<p class="text-muted-foreground leading-relaxed mb-4">$1</p>',
      )
  }

  // Auto-scroll to bottom when new messages are added
  $: if ($messages.length > 0) {
    setTimeout(() => {
      const messagesContainer = document.querySelector(".messages-container")
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  // Animated placeholder rotation
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 3000)
    
    return () => clearInterval(interval)
  })
  
  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
  }
  
  function downloadAsCSV(content: string) {
    const blob = new Blob([content], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `seo-keywords-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  function generateFollowUp(suggestion: string, originalContent: string) {
    let followUpPrompt = ""
    switch(suggestion) {
      case "cluster":
        followUpPrompt = "Cluster these keywords by search intent and create content groups"
        break
      case "blog":
        followUpPrompt = "Generate a blog content outline using the top 10 keywords from this analysis"
        break
      case "competition":
        followUpPrompt = "Analyze the competition level and ranking difficulty for each keyword group"
        break
    }
    input = followUpPrompt
    sendMessage()
  }
</script>

<svelte:head>
  <title>SEO Strategist - AI Agent</title>
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Target class="w-6 h-6 animate-pulse" style="color: var(--primary-foreground);" />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">
              Lexi - SEO Strategist
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              Powered by Lexi – your AI SEO Sidekick
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <TrendingUp
              class="w-4 h-4"
              style="color: var(--accent-foreground);"
            />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);"
              >Keyword Intelligence</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">SEO Agent</span>
    </nav>
  </div>

  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    <!-- Full Width Conversation Panel -->
    <div class="h-full">
      <div
        class="card-brutal p-0 chat-container h-full"
        style="background: var(--card);"
      >
          <!-- Messages Area -->
          <div class="messages-wrapper messages-container">
            <div class="space-y-6">
              {#if $messages.length === 0}
                <div class="text-center py-12">
                  <div
                    class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2"
                    style="background: var(--muted); border-color: var(--border);"
                  >
                    <Target
                      class="w-8 h-8"
                      style="color: var(--muted-foreground);"
                    />
                  </div>
                  <h3
                    class="text-xl font-bold mb-2"
                    style="color: var(--foreground);"
                  >
                    Start Your SEO Research
                  </h3>
                  <p
                    class="font-medium mb-6"
                    style="color: var(--muted-foreground);"
                  >
                    Get keyword analysis and SEO strategy for any business
                  </p>

                  <!-- Interactive Cards -->
                  <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                    {#each interactiveCards as card}
                      <button
                        on:click={() => {
                          input = card.prompt
                        }}
                        class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group"
                        style="background: var(--card); border-color: var(--border);"
                      >
                        <div class="flex items-center gap-3 mb-3">
                          <div
                            class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform"
                            style="background: var(--primary); border-color: var(--border);"
                          >
                            <svelte:component this={card.icon} class="w-4 h-4" style="color: var(--primary-foreground);" />
                          </div>
                          <h4 class="font-bold text-sm" style="color: var(--foreground);">
                            {card.title}
                          </h4>
                        </div>
                        <p class="text-xs mb-3" style="color: var(--muted-foreground);">
                          {card.description}
                        </p>
                        <div class="text-xs font-mono p-2 border-2 rounded" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);">
                          {card.prompt}
                        </div>
                      </button>
                    {/each}
                  </div>
                </div>
              {/if}

              {#each $messages as message}
                <div
                  class="flex gap-4 {message.role === 'user'
                    ? 'flex-row-reverse'
                    : ''}"
                >
                  <div
                    class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                    style="background: var(--{message.role === 'user'
                      ? 'primary'
                      : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    {#if message.role === "user"}
                      <User
                        class="w-5 h-5"
                        style="color: var(--primary-foreground);"
                      />
                    {:else}
                      <Bot
                        class="w-5 h-5"
                        style="color: var(--secondary-foreground);"
                      />
                    {/if}
                  </div>

                  <div class="flex-1 max-w-3xl">
                    <div class="flex items-center gap-2 mb-2">
                      <span
                        class="text-sm font-bold"
                        style="color: var(--foreground);"
                      >
                        {message.role === "user" ? "You" : "SEO Strategist"}
                      </span>
                      <div class="flex items-center gap-1">
                        <Clock
                          class="w-3 h-3"
                          style="color: var(--muted-foreground);"
                        />
                        <span
                          class="text-xs"
                          style="color: var(--muted-foreground);"
                        >
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      {#if message.role === "assistant" && message.isReport}
                        <button
                          on:click={() => downloadAsMarkdown(message)}
                          class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                          title="Download as Markdown"
                        >
                          <Download class="w-3 h-3" />
                          Download
                        </button>
                      {/if}
                    </div>

                    {#if message.role === "user"}
                      <div
                        class="p-4 border-2"
                        style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                      >
                        <p
                          class="font-medium"
                          style="color: var(--primary-foreground);"
                        >
                          {message.content}
                        </p>
                      </div>
                    {:else}
                      <div
                        class="p-6 border-2 mb-4"
                        style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"
                      >
                        <div class="prose prose-sm max-w-none">
                          {@html formatContent(message.content)}
                        </div>
                      </div>
                      
                      <!-- Follow-up Actions -->
                      <div class="flex flex-wrap gap-2 mb-4">
                        <button
                          on:click={() => generateFollowUp("cluster", message.content)}
                          class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                        >
                          <BarChart class="w-3 h-3" />
                          Cluster by Intent
                        </button>
                        <button
                          on:click={() => copyToClipboard(message.content)}
                          class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                        >
                          <Copy class="w-3 h-3" />
                          Copy
                        </button>
                        <button
                          on:click={() => downloadAsCSV(message.content)}
                          class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                        >
                          <FileDown class="w-3 h-3" />
                          Export CSV
                        </button>
                        <button
                          on:click={() => generateFollowUp("blog", message.content)}
                          class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                        >
                          <BookOpen class="w-3 h-3" />
                          Generate Blog Outline
                        </button>
                      </div>
                    {/if}
                  </div>
                </div>
              {/each}

              {#if isLoading}
                <div class="flex gap-4">
                  <div
                    class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                    style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    <Bot
                      class="w-5 h-5"
                      style="color: var(--secondary-foreground);"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <span
                        class="text-sm font-bold"
                        style="color: var(--foreground);">SEO Strategist</span
                      >
                      <span
                        class="text-xs"
                        style="color: var(--muted-foreground);"
                        >Analyzing keywords...</span
                      >
                    </div>
                    <div
                      class="p-4 border-2"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 rounded-full animate-pulse"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-2000"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-4000"
                          style="background: var(--primary);"
                        ></div>
                        <span
                          class="text-sm font-medium"
                          style="color: var(--muted-foreground);"
                          >Researching keywords and generating SEO strategy...</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <!-- Input Area -->
          <div class="input-wrapper p-6">
            <!-- Output Format Toggle -->
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-bold" style="color: var(--foreground);">SEO Analysis</h2>
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium" style="color: var(--muted-foreground);">Output Format:</span>
                <div class="flex border-2" style="border-color: var(--border); background: var(--background);">
                  <button
                    on:click={() => outputFormat = "summary"}
                    class="px-3 py-1 text-sm font-medium transition-colors {outputFormat === 'summary' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                  >
                    Summary
                  </button>
                  <button
                    on:click={() => outputFormat = "table"}
                    class="px-3 py-1 text-sm font-medium transition-colors border-l border-r {outputFormat === 'table' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                    style="border-color: var(--border);"
                  >
                    Table
                  </button>
                  <button
                    on:click={() => outputFormat = "blog"}
                    class="px-3 py-1 text-sm font-medium transition-colors {outputFormat === 'blog' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                  >
                    Blog-ready
                  </button>
                </div>
              </div>
            </div>

            <!-- Prompt Enhancer Filters -->
            <div class="mb-4">
              <button
                on:click={() => showFilters = !showFilters}
                class="btn-secondary px-3 py-2 text-sm flex items-center gap-2 mb-3"
              >
                <Filter class="w-4 h-4" />
                Prompt Enhancer
                <span class="text-xs {showFilters ? 'rotate-180' : ''} transition-transform">▼</span>
              </button>
              
              {#if showFilters}
                <div class="grid md:grid-cols-3 gap-4 p-4 border-2 mb-4" style="background: var(--muted); border-color: var(--border);">
                  <div>
                    <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Audience</label>
                    <select
                      bind:value={targetAudience}
                      class="w-full p-2 border-2 text-xs"
                      style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                    >
                      <option value="">Select audience</option>
                      <option value="B2B SaaS">B2B SaaS</option>
                      <option value="E-commerce">E-commerce</option>
                      <option value="Local business">Local business</option>
                      <option value="Content creators">Content creators</option>
                      <option value="Enterprise">Enterprise</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Region Focus</label>
                    <input
                      bind:value={regionFocus}
                      placeholder="e.g., US, Europe, Global"
                      class="w-full p-2 border-2 text-xs"
                      style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Funnel Stage</label>
                    <div class="flex border-2" style="border-color: var(--border); background: var(--background);">
                      <button
                        on:click={() => funnelStage = "awareness"}
                        class="px-2 py-1 text-xs font-medium transition-colors {funnelStage === 'awareness' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                      >
                        Awareness
                      </button>
                      <button
                        on:click={() => funnelStage = "consideration"}
                        class="px-2 py-1 text-xs font-medium transition-colors border-l border-r {funnelStage === 'consideration' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                        style="border-color: var(--border);"
                      >
                        Consideration
                      </button>
                      <button
                        on:click={() => funnelStage = "decision"}
                        class="px-2 py-1 text-xs font-medium transition-colors {funnelStage === 'decision' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
                      >
                        Decision
                      </button>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
            
            <div class="flex gap-4">
              <div class="flex-1 relative">
                <textarea
                  bind:value={input}
                  on:keydown={handleKeyDown}
                  placeholder={currentPlaceholder}
                  class="input-brutal enhanced-input flex-1 resize-none min-h-[100px] p-4 w-full"
                  disabled={isLoading}
                ></textarea>
              </div>
              <button
                on:click={sendMessage}
                disabled={!input.trim() || isLoading}
                class="btn-primary px-6 py-4 font-bold flex items-center gap-2 self-end"
              >
                {#if isLoading}
                  <div
                    class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
                  ></div>
                {:else}
                  <Target class="w-4 h-4 animate-pulse" />
                {/if}
                Analyze
              </button>
            </div>
          </div>
      </div>
    </div>
  </div>

</div>

<style>
  /* Custom scrollbar styles */
  .messages-container {
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--muted);
  }

  .messages-container::-webkit-scrollbar {
    width: 8px;
  }

  .messages-container::-webkit-scrollbar-track {
    background: var(--muted);
    border-left: 2px solid var(--border);
  }

  .messages-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 0;
  }

  .messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }

  /* Fixed layout for chat container */
  .chat-container {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .messages-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 300px; /* Space for input area */
    overflow-y: auto;
    padding: 1.5rem;
  }

  .input-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 300px;
    border-top: 2px solid var(--border);
    background: var(--card);
  }
  
  /* Enhanced input styling */
  .enhanced-input {
    transition: all 0.3s ease;
  }
  
  .enhanced-input:focus {
    box-shadow: 0 0 0 3px var(--primary), 0 0 20px var(--primary);
    border-color: var(--primary);
  }
  
  .enhanced-input::placeholder {
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
  
  .enhanced-input:focus::placeholder {
    opacity: 0.5;
  }
  
  /* Animated icon effects */
  .w-12.h-12 .animate-pulse {
    animation: iconPulse 2s infinite;
  }
  
  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1);
      filter: drop-shadow(0 0 0 var(--primary));
    }
    50% {
      transform: scale(1.05);
      filter: drop-shadow(0 0 8px var(--primary));
    }
  }
  
  /* Button hover effects */
  .btn-primary:hover .animate-pulse {
    animation: iconGlow 1s infinite;
  }
  
  @keyframes iconGlow {
    0%, 100% {
      filter: drop-shadow(0 0 4px var(--primary-foreground));
    }
    50% {
      filter: drop-shadow(0 0 12px var(--primary-foreground));
    }
  }
</style>
