<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import {
    Search,
    Download,
    Building2,
    TrendingUp,
    Clock,
    User,
    Bot,
    ChevronRight,
    BarChart3,
    Target,
    Brain,
    Zap,
    MessageSquare,
    Eye,
  } from "lucide-svelte"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  // let selectedMessageId = "" // Not currently used
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let outputFormat = "executive"
  // let agentMode = "general" // Removed agent mode functionality
  
  // Animated placeholder examples
  const placeholderExamples = [
    "Compare Drift and Intercom's messaging and channel mix based on the last 30 days...",
    "How is Adobe marketing Firefly across its channels based on recent data...",
    "What influencer or social campaigns has Notion run recently...",
    "Map Figma's demand-gen strategy from 2022 to now...",
    "Analyze Stripe's developer marketing evolution over the past quarter..."
  ]
  
  // Marketing Research Prompt Templates
  const marketingPrompts = [
    "Compare Drift and Intercom's messaging and channel mix based on the last 30 days",
    "How is Adobe marketing Firefly across its channels based on recent data?",
    "What influencer or social campaigns has Notion run recently?",
    "Map Figma's demand-gen strategy from 2022 to now"
  ]

  // Quick start templates
  const quickStartTemplates = [
    {
      icon: BarChart3,
      title: "Company Snapshot",
      description: "Get a summary of performance, team, and competitors",
      prompt: "Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics."
    },
    {
      icon: Target,
      title: "Go-to-Market Audit",
      description: "Evaluate positioning, messaging, channels, and campaigns",
      prompt: "Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience."
    },
    {
      icon: Brain,
      title: "Brand Perception & Category",
      description: "Analyze how a brand is perceived in its space",
      prompt: "Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception."
    }
  ]

  // Output format options
  const outputFormats = [
    { value: "executive", label: "Executive Summary", description: "Bullet points + key insights" },
    { value: "slide-ready", label: "Slide-ready", description: "Sections + headers for export" },
    { value: "battlecard", label: "Competitive Battlecard", description: "Strategic comparison format" }
  ]

  // Removed filter options - functionality not needed

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix = "[Executive Summary Format] Provide bullet points and key insights. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix = "[Slide-ready Format] Structure with clear sections and headers for presentation export. "
    } else if (outputFormat === "battlecard") {
      formatPrefix = "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Add assistant response to chat
      const assistantMessageId = generateId()
      messages.update((msgs) => [
        ...msgs,
        {
          id: assistantMessageId,
          role: "assistant",
          content: data.response,
          timestamp: new Date(),
          isReport: true,
        },
      ])

      // selectedMessageId = assistantMessageId // Not currently used
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but Athena encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }
  
  function handleTemplateClick(template: typeof quickStartTemplates[0]) {
    input = template.prompt
    // Focus the textarea
    const textarea = document.querySelector('textarea')
    if (textarea) {
      textarea.focus()
    }
  }

  function handlePromptClick(prompt: string) {
    input = prompt
    const textarea = document.querySelector('textarea')
    if (textarea) {
      textarea.focus()
    }
  }

  // Removed toggleFilters function - not needed

  function extractInsights(content: string): { summary: string; insights: string[]; badges: string[] } {
    // Simple insight extraction - in a real app this would be more sophisticated
    const lines = content.split('\n').filter(line => line.trim())
    const summary = lines.slice(0, 2).join(' ').substring(0, 200) + '...'
    
    const insights = lines
      .filter(line => line.includes('key') || line.includes('important') || line.includes('significant'))
      .slice(0, 3)
    
    const badges = []
    if (content.includes('growth') || content.includes('increase')) badges.push('↑ Trending')
    if (content.includes('insight') || content.includes('analysis')) badges.push('💡 Insight')
    if (content.includes('challenge') || content.includes('weakness')) badges.push('⚠ Weakness')
    
    return { summary, insights, badges }
  }
  
  // Animated placeholder effect
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 4000)
    
    return () => clearInterval(interval)
  })

  function downloadAsMarkdown(message: Message) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${companyName || "research-report"}-${timestamp}.md`

    const markdownContent = `# Marketing Research Report by Athena
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}
**Format:** ${outputFormat.charAt(0).toUpperCase() + outputFormat.slice(1)}

---

${message.content}

---

*Report generated by Athena - Your AI Market Researcher*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Company:") || line.includes("Company Name:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("Research") &&
        !line.includes("Report")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Enhanced markdown to HTML conversion for professional display
    let formatted = content
      // Headers
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>')
      
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold" style="color: var(--foreground);">$1</strong>')
      
      // Italic text
      .replace(/\*(.*?)\*/g, '<em class="italic" style="color: var(--muted-foreground);">$1</em>')
      
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>')
      
      // Inline code
      .replace(/`([^`]+)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>')
      
      // Tables
      .replace(/\|(.+)\|/g, (match) => {
        const cells = match.split('|').filter(cell => cell.trim()).map(cell => cell.trim())
        return '<tr>' + cells.map(cell => `<td class="border border-border px-3 py-2" style="color: var(--foreground);">${cell}</td>`).join('') + '</tr>'
      })
      
      // Lists
      .replace(/^[\s]*[-*+] (.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>')
      .replace(/^[\s]*\d+\. (.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: decimal;">$1</li>')
      
      // Blockquotes
      .replace(/^> (.+)$/gim, '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>')
      
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>')
      
      // Line breaks and paragraphs
      .replace(/\n\n/g, '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">')
      .replace(/\n/g, '<br>')
    
    // Wrap content in paragraph if it doesn't start with a block element
    if (!formatted.startsWith('<h') && !formatted.startsWith('<p') && !formatted.startsWith('<ul') && !formatted.startsWith('<ol') && !formatted.startsWith('<blockquote')) {
      formatted = '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' + formatted + '</p>'
    }
    
    // Wrap lists in proper ul/ol tags
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes('list-style-type: disc')) {
        return '<ul class="mb-4">' + match + '</ul>'
      } else if (match.includes('list-style-type: decimal')) {
        return '<ol class="mb-4">' + match + '</ol>'
      }
      return match
    })
    
    // Wrap table rows in table
    if (formatted.includes('<tr>')) {
      formatted = formatted.replace(/(<tr>.*?<\/tr>)/gs, '<table class="w-full border-collapse border border-border my-4">$1</table>')
    }
    
    return formatted
  }

  // Auto-scroll to bottom when new messages are added
  $: if ($messages.length > 0) {
    setTimeout(() => {
      const messagesContainer = document.querySelector(".messages-container")
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  // Removed - replaced with quickStartTemplates
</script>

<svelte:head>
  <title>Athena - AI Market Researcher</title>
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Building2
              class="w-6 h-6"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1 class="text-3xl font-black flex items-center gap-3" style="color: var(--foreground);">
              <Zap class="w-8 h-8" style="color: var(--primary);" />
              Athena
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              Your AI market researcher
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <TrendingUp
              class="w-4 h-4"
              style="color: var(--accent-foreground);"
            />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);">Real-time Data</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Athena</span>
    </nav>
  </div>

  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    <!-- Full Width Conversation Panel -->
    <div class="h-full">
      <div
        class="card-brutal p-0 chat-container h-full"
        style="background: var(--card);"
      >
          <!-- Messages Area -->
          <div class="messages-wrapper messages-container">
            <div class="space-y-6">
              {#if $messages.length === 0}
                <div class="text-center py-8">
                  <div class="flex items-center justify-center gap-2 mb-6">
                    <Zap class="w-6 h-6" style="color: var(--primary);" />
                    <h3
                      class="text-2xl font-bold"
                      style="color: var(--foreground);"
                    >
                      Ready to Research
                    </h3>
                  </div>
                  <p
                    class="font-medium mb-8 max-w-2xl mx-auto"
                    style="color: var(--muted-foreground);"
                  >
                    Get marketing intelligence on any company. Choose a template below or ask your custom question.
                  </p>

                  <!-- Marketing Research Prompts -->
                  <div class="mb-8">
                    <h4 class="text-sm font-bold mb-4 text-center" style="color: var(--muted-foreground);">
                      Marketing Research Prompts
                    </h4>
                    <div class="grid md:grid-cols-2 gap-3 max-w-4xl mx-auto mb-6">
                      {#each marketingPrompts as prompt}
                        <button
                          on:click={() => handlePromptClick(prompt)}
                          class="prompt-card p-4 border-2 transition-all duration-200 hover:-translate-y-1 text-left group"
                          style="background: var(--background); border-color: var(--border); border-radius: 0.5rem;"
                        >
                          <span class="text-sm font-medium group-hover:text-primary transition-colors" style="color: var(--foreground);">
                            {prompt}
                          </span>
                          <div class="flex items-center gap-1 mt-2 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                            <span class="text-xs font-bold">Try This</span>
                            <ChevronRight class="w-3 h-3" />
                          </div>
                        </button>
                      {/each}
                    </div>
                  </div>

                  <!-- Quick Start Template Cards -->
                  <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                    {#each quickStartTemplates as template}
                      <button
                        on:click={() => handleTemplateClick(template)}
                        class="template-card p-6 border-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg text-left group"
                        style="background: var(--card); border-color: var(--border); border-radius: 0.5rem;"
                      >
                        <div class="mb-3">
                          <svelte:component this={template.icon} class="w-8 h-8" style="color: var(--primary);" />
                        </div>
                        <h4 class="text-lg font-bold mb-2 group-hover:text-primary transition-colors" style="color: var(--foreground);">
                          {template.title}
                        </h4>
                        <p class="text-sm leading-relaxed" style="color: var(--muted-foreground);">
                          {template.description}
                        </p>
                        <div class="flex items-center gap-1 mt-3 text-primary opacity-0 group-hover:opacity-100 transition-opacity">
                          <span class="text-xs font-bold">Use Template</span>
                          <ChevronRight class="w-3 h-3" />
                        </div>
                      </button>
                    {/each}
                  </div>
                </div>
              {/if}

              {#each $messages as message}
                <div
                  class="flex gap-4 {message.role === 'user'
                    ? 'flex-row-reverse'
                    : ''}"
                >
                  <div
                    class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                    style="background: var(--{message.role === 'user'
                      ? 'primary'
                      : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    {#if message.role === "user"}
                      <User
                        class="w-5 h-5"
                        style="color: var(--primary-foreground);"
                      />
                    {:else}
                      <Bot
                        class="w-5 h-5"
                        style="color: var(--secondary-foreground);"
                      />
                    {/if}
                  </div>

                  <div class="flex-1 max-w-3xl">
                    <div class="flex items-center gap-2 mb-2">
                      <span
                        class="text-sm font-bold"
                        style="color: var(--foreground);"
                      >
                        {message.role === "user" ? "You" : "Athena"}
                      </span>
                      <div class="flex items-center gap-1">
                        <Clock
                          class="w-3 h-3"
                          style="color: var(--muted-foreground);"
                        />
                        <span
                          class="text-xs"
                          style="color: var(--muted-foreground);"
                        >
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      {#if message.role === "assistant" && message.isReport}
                        <button
                          on:click={() => downloadAsMarkdown(message)}
                          class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                          title="Download as Markdown"
                        >
                          <Download class="w-3 h-3" />
                          Download
                        </button>
                      {/if}
                    </div>

                    {#if message.role === "user"}
                      <div
                        class="p-4 border-2"
                        style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                      >
                        <p
                          class="font-medium"
                          style="color: var(--primary-foreground);"
                        >
                          {message.content}
                        </p>
                      </div>
                    {:else}
                      {@const insights = extractInsights(message.content)}
                      <div
                        class="border-2"
                        style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"
                      >
                        <!-- TL;DR Summary -->
                        {#if insights.summary}
                          <div class="p-4 border-b-2 border-border bg-muted/50">
                            <div class="flex items-start gap-2">
                              <span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded">TL;DR</span>
                              <div class="text-sm font-medium formatted-summary" style="color: var(--foreground);">{@html formatContent(insights.summary)}</div>
                            </div>
                          </div>
                        {/if}
                        
                        <!-- Badges -->
                        {#if insights.badges.length > 0}
                          <div class="px-6 pt-4 pb-2">
                            <div class="flex flex-wrap gap-2">
                              {#each insights.badges as badge}
                                <span class="text-xs font-bold px-2 py-1 border border-border rounded" style="background: var(--accent); color: var(--accent-foreground);">
                                  {badge}
                                </span>
                              {/each}
                            </div>
                          </div>
                        {/if}
                        
                        <!-- Main Content -->
                        <div class="p-6">
                          <div class="formatted-content max-w-none">
                            {@html formatContent(message.content)}
                          </div>
                        </div>
                        
                        <!-- Follow-up Actions -->
                        <div class="px-6 pb-4 border-t border-border">
                          <div class="flex flex-wrap gap-2 mt-4">
                            <button 
                              on:click={() => input = `Compare this analysis with their main competitor`}
                              class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                            >
                              <Eye class="w-3 h-3" />
                              Compare with competitor
                            </button>
                            <button 
                              on:click={() => input = `Add visual charts and graphs to this analysis`}
                              class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                            >
                              <BarChart3 class="w-3 h-3" />
                              Add visuals
                            </button>
                            <button 
                              on:click={() => input = `Turn this analysis into presentation slides`}
                              class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                            >
                              <MessageSquare class="w-3 h-3" />
                              Turn into slides
                            </button>
                          </div>
                        </div>
                      </div>
                    {/if}
                  </div>
                </div>
              {/each}

              {#if isLoading}
                <div class="flex gap-4">
                  <div
                    class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                    style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    <Bot
                      class="w-5 h-5"
                      style="color: var(--secondary-foreground);"
                    />
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                      <span
                        class="text-sm font-bold"
                        style="color: var(--foreground);"
                        >Athena</span
                      >
                      <span
                        class="text-xs"
                        style="color: var(--muted-foreground);"
                        >Scanning recent campaigns...</span
                      >
                    </div>
                    <div
                      class="p-4 border-2"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 rounded-full animate-pulse"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-2000"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-4000"
                          style="background: var(--primary);"
                        ></div>
                        <span
                          class="text-sm font-medium"
                          style="color: var(--muted-foreground);"
                          >Reading earnings call notes and marketing data...</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <!-- Spotlight-Style Input Area -->
          <div class="input-wrapper p-6">
            <!-- Output Format Selector -->
            <div class="flex items-center gap-2 mb-4">
              <span class="text-sm font-bold" style="color: var(--muted-foreground);">Format:</span>
              <select 
                bind:value={outputFormat} 
                class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded"
                style="border-radius: 0.375rem;"
              >
                {#each outputFormats as format}
                  <option value={format.value}>{format.label}</option>
                {/each}
              </select>
            </div>

            <div class="relative">
              <div class="spotlight-input-container">
                <textarea
                  bind:value={input}
                  on:keydown={handleKeyDown}
                  placeholder={currentPlaceholder}
                  class="spotlight-input flex-1 resize-none min-h-[120px] p-6 pr-32 text-lg"
                  disabled={isLoading}
                ></textarea>
                <button
                  on:click={sendMessage}
                  disabled={!input.trim() || isLoading}
                  class="spotlight-button"
                >
                  {#if isLoading}
                    <div
                      class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"
                    ></div>
                  {:else}
                    <Search class="w-5 h-5" />
                  {/if}
                  <span class="font-bold">Research</span>
                </button>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Custom scrollbar styles */
  .messages-container {
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--muted);
  }

  .messages-container::-webkit-scrollbar {
    width: 8px;
  }

  .messages-container::-webkit-scrollbar-track {
    background: var(--muted);
    border-left: 2px solid var(--border);
  }

  .messages-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 0;
  }

  .messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }

  /* Fixed layout for chat container */
  .chat-container {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .messages-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 220px; /* Space for input area */
    overflow-y: auto;
    padding: 1.5rem;
  }

  .input-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 220px;
    border-top: 2px solid var(--border);
    background: var(--card);
  }

  /* Spotlight-style input */
  .spotlight-input-container {
    position: relative;
    border: 2px solid var(--border);
    border-radius: 0.5rem;
    background: var(--background);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
  }

  .spotlight-input-container:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
                0 0 20px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
                var(--shadow-lg);
    transform: translateY(-2px);
  }

  .spotlight-input {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    font-family: 'Plus Jakarta Sans', system-ui, sans-serif;
    color: var(--foreground);
    line-height: 1.6;
  }

  .spotlight-input::placeholder {
    color: var(--muted-foreground);
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  @keyframes placeholderFade {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.8; }
  }

  .spotlight-input:focus::placeholder {
    animation: placeholderFade 2s ease-in-out infinite;
  }

  .spotlight-button {
    position: absolute;
    right: 6px;
    bottom: 6px;
    padding: 0.75rem 1.5rem;
    background: var(--primary);
    color: var(--primary-foreground);
    border: 2px solid var(--border);
    border-radius: 0.375rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
  }

  .spotlight-button:hover:not(:disabled) {
    transform: translate(-2px, -2px);
    box-shadow: 4px 4px 0 var(--border);
  }

  .spotlight-button:active:not(:disabled) {
    transform: translate(0, 0);
    box-shadow: var(--shadow-sm);
  }

  .spotlight-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Template cards */
  .template-card {
    position: relative;
    overflow: hidden;
  }

  .template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(var(--primary-rgb, 59, 130, 246), 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .template-card:hover::before {
    opacity: 1;
  }

  .template-card:hover {
    box-shadow: 6px 6px 0 var(--border);
    border-color: var(--primary);
  }

  /* Prompt cards */
  .prompt-card {
    position: relative;
    overflow: hidden;
  }

  .prompt-card:hover {
    box-shadow: 4px 4px 0 var(--border);
    border-color: var(--primary);
  }

  /* Custom select styling */
  select {
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 0.65rem auto;
  }

  /* Formatted content styling */
  .formatted-content {
    line-height: 1.7;
  }

  /* TL;DR summary specific formatting */
  .formatted-summary {
    line-height: 1.5;
  }

  .formatted-summary h1,
  .formatted-summary h2,
  .formatted-summary h3,
  .formatted-summary h4 {
    margin: 0.25rem 0;
    font-size: inherit;
    font-weight: 600;
  }

  .formatted-summary p {
    margin: 0.25rem 0;
    font-size: inherit;
  }

  .formatted-summary ul,
  .formatted-summary ol {
    margin: 0.25rem 0;
    padding-left: 1rem;
  }

  .formatted-summary li {
    margin: 0;
  }

  .formatted-content h1:first-child,
  .formatted-content h2:first-child,
  .formatted-content h3:first-child {
    margin-top: 0;
  }

  .formatted-content ul,
  .formatted-content ol {
    padding-left: 1.5rem;
  }

  .formatted-content table {
    font-size: 0.875rem;
  }

  .formatted-content blockquote {
    margin: 1rem 0;
  }

  .formatted-content pre {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .formatted-content code {
    font-size: 0.85em;
  }

  /* Content animations */
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Animation keyframes for future enhancements */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scan {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Keyboard shortcut style */
  kbd {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 0.75rem;
    font-weight: 500;
  }

  /* Mode selector active state */
  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0.2); }
    70% { box-shadow: 0 0 0 6px rgba(var(--primary-rgb, 59, 130, 246), 0); }
    100% { box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0); }
  }

  /* Smooth transitions for mode changes */
  .messages-container {
    transition: all 0.3s ease;
  }
</style>
